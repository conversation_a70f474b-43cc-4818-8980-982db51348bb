"""
Vector Database Storage

This module handles the storage and retrieval of vector embeddings using
ChromaDB as the vector database backend.

Key classes:
- VectorStore: Abstract base for vector storage
- ChromaVectorStore: ChromaDB implementation
- StorageManager: High-level storage operations
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class VectorStore(ABC):
    """Abstract base class for vector storage."""

    @abstractmethod
    def create_collection(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Create a new collection."""
        pass

    @abstractmethod
    def add_documents(self, collection_name: str, documents: List[Dict[str, Any]]) -> None:
        """Add documents to collection."""
        pass

    @abstractmethod
    def query(
        self,
        collection_name: str,
        query_embedding: List[float],
        n_results: int = 10,
        where: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Query collection for similar documents."""
        pass

    @abstractmethod
    def delete_collection(self, name: str) -> None:
        """Delete a collection."""
        pass

    @abstractmethod
    def get_collection_info(self, name: str) -> Dict[str, Any]:
        """Get information about a collection."""
        pass


class ChromaVectorStore(VectorStore):
    """ChromaDB implementation of vector storage."""

    def __init__(self, db_path: str = "./chroma_db"):
        """
        Initialize ChromaDB vector store.

        Args:
            db_path: Path to ChromaDB database
        """
        self.db_path = Path(db_path)
        self._client = None

    def _get_client(self):
        """Lazy load ChromaDB client."""
        if self._client is None:
            try:
                import chromadb

                self.db_path.mkdir(parents=True, exist_ok=True)
                self._client = chromadb.PersistentClient(path=str(self.db_path))
                logger.info(f"Initialized ChromaDB client at {self.db_path}")
            except ImportError:
                raise ImportError("chromadb not installed. Run: pip install chromadb")
        return self._client

    def create_collection(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Create a new ChromaDB collection."""
        client = self._get_client()

        try:
            # Delete existing collection if it exists
            try:
                client.delete_collection(name)
                logger.info(f"Deleted existing collection: {name}")
            except Exception:
                pass  # Collection doesn't exist

            client.create_collection(name=name, metadata=metadata or {})
            logger.info(f"Created collection: {name}")

        except Exception as e:
            logger.error(f"Error creating collection {name}: {e}")
            raise

    def add_documents(self, collection_name: str, documents: List[Dict[str, Any]]) -> None:
        """Add documents to ChromaDB collection."""
        if not documents:
            return

        client = self._get_client()
        collection = client.get_collection(collection_name)

        # Prepare data for ChromaDB
        ids = [doc["id"] for doc in documents]
        embeddings = [doc["embedding"] for doc in documents]
        documents_text = [doc["content"] for doc in documents]
        metadatas = [doc["metadata"] for doc in documents]

        # Convert metadata to strings for ChromaDB compatibility
        for metadata in metadatas:
            for key, value in metadata.items():
                if isinstance(value, (list, dict)):
                    metadata[key] = json.dumps(value)
                elif value is None:
                    metadata[key] = ""
                else:
                    metadata[key] = str(value)

        try:
            collection.add(
                ids=ids, embeddings=embeddings, documents=documents_text, metadatas=metadatas
            )
            logger.info(f"Added {len(documents)} documents to collection {collection_name}")

        except Exception as e:
            logger.error(f"Error adding documents to collection {collection_name}: {e}")
            raise

    def query(
        self,
        collection_name: str,
        query_embedding: List[float],
        n_results: int = 10,
        where: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Query ChromaDB collection."""
        client = self._get_client()
        collection = client.get_collection(collection_name)

        try:
            results = collection.query(
                query_embeddings=[query_embedding], n_results=n_results, where=where
            )

            # Format results
            formatted_results = []
            for i in range(len(results["ids"][0])):
                result = {
                    "id": results["ids"][0][i],
                    "content": results["documents"][0][i],
                    "metadata": results["metadatas"][0][i],
                    "distance": results["distances"][0][i] if "distances" in results else None,
                }
                formatted_results.append(result)

            return formatted_results

        except Exception as e:
            logger.error(f"Error querying collection {collection_name}: {e}")
            raise

    def delete_collection(self, name: str) -> None:
        """Delete ChromaDB collection."""
        client = self._get_client()

        try:
            client.delete_collection(name)
            logger.info(f"Deleted collection: {name}")
        except Exception as e:
            logger.error(f"Error deleting collection {name}: {e}")
            raise

    def get_collection_info(self, name: str) -> Dict[str, Any]:
        """Get ChromaDB collection information."""
        client = self._get_client()

        try:
            collection = client.get_collection(name)
            count = collection.count()

            return {"name": name, "count": count, "metadata": collection.metadata}
        except Exception as e:
            logger.error(f"Error getting collection info for {name}: {e}")
            raise


class StorageManager:
    """High-level storage operations manager."""

    def __init__(self, vector_store: VectorStore, collection_name: str = "codebase_chunks"):
        """
        Initialize storage manager.

        Args:
            vector_store: Vector store implementation
            collection_name: Name of the collection to use
        """
        self.vector_store = vector_store
        self.collection_name = collection_name

    def initialize_storage(self, model_info: Dict[str, Any]) -> None:
        """Initialize storage with model information."""
        metadata = {
            "description": "Semantic chunks from codebase",
            "embedding_model": model_info.get("model_name", "unknown"),
            "embedding_dimension": model_info.get("dimension", 0),
            "chunk_strategy": "tree-sitter-semantic",
            "created_at": datetime.now().isoformat(),
        }

        self.vector_store.create_collection(self.collection_name, metadata)
        logger.info(f"Initialized storage collection: {self.collection_name}")

    def store_chunks(self, processed_chunks: List[Dict[str, Any]]) -> None:
        """Store processed chunks in vector database."""
        if not processed_chunks:
            logger.warning("No chunks to store")
            return

        self.vector_store.add_documents(self.collection_name, processed_chunks)
        logger.info(f"Stored {len(processed_chunks)} chunks")

    def search_similar(
        self,
        query_embedding: List[float],
        limit: int = 10,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """Search for similar chunks."""
        return self.vector_store.query(
            self.collection_name, query_embedding, n_results=limit, where=filters
        )

    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        return self.vector_store.get_collection_info(self.collection_name)

    def clear_storage(self) -> None:
        """Clear all stored data."""
        self.vector_store.delete_collection(self.collection_name)
        logger.info(f"Cleared storage collection: {self.collection_name}")


def create_vector_store(store_type: str = "chroma", **kwargs) -> VectorStore:
    """
    Factory function to create vector stores.

    Args:
        store_type: Type of vector store ('chroma')
        **kwargs: Store-specific arguments

    Returns:
        Vector store instance
    """
    if store_type.lower() == "chroma":
        return ChromaVectorStore(**kwargs)
    else:
        raise ValueError(f"Unsupported vector store type: {store_type}")
