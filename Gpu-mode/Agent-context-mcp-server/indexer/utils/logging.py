"""
Logging Setup and Configuration

This module provides logging configuration and utilities for the RAG indexer.
It supports structured logging, progress tracking, and performance metrics.

Key functions:
- setup_logging: Configure logging for the application
- get_logger: Get a configured logger instance
- ProgressTracker: Track progress of long operations
"""

import logging
import logging.handlers
import sys
import time
from pathlib import Path
from typing import Optional
from contextlib import contextmanager
import json


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""

    def __init__(self, include_extra: bool = True):
        """
        Initialize structured formatter.

        Args:
            include_extra: Whether to include extra fields in log records
        """
        super().__init__()
        self.include_extra = include_extra

    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured data."""
        # Base log data
        log_data = {
            "timestamp": self.formatTime(record),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
        }

        # Add location info for debug level
        if record.levelno <= logging.DEBUG:
            log_data.update(
                {
                    "file": record.filename,
                    "line": record.lineno,
                    "function": record.funcName,
                }
            )

        # Add exception info if present
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)

        # Add extra fields
        if self.include_extra:
            for key, value in record.__dict__.items():
                if key not in [
                    "name",
                    "msg",
                    "args",
                    "levelname",
                    "levelno",
                    "pathname",
                    "filename",
                    "module",
                    "lineno",
                    "funcName",
                    "created",
                    "msecs",
                    "relativeCreated",
                    "thread",
                    "threadName",
                    "processName",
                    "process",
                    "getMessage",
                    "exc_info",
                    "exc_text",
                    "stack_info",
                ]:
                    log_data[key] = value

        return json.dumps(log_data, default=str)


class ColoredFormatter(logging.Formatter):
    """Colored formatter for console output."""

    # Color codes
    COLORS = {
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[35m",  # Magenta
        "RESET": "\033[0m",  # Reset
    }

    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, "")
        reset_color = self.COLORS["RESET"]

        # Create colored level name
        colored_level = f"{level_color}{record.levelname}{reset_color}"

        # Format timestamp
        timestamp = self.formatTime(record, "%H:%M:%S")

        # Format message
        message = record.getMessage()

        # Add location for debug
        if record.levelno <= logging.DEBUG:
            location = f" [{record.filename}:{record.lineno}]"
        else:
            location = ""

        return f"{timestamp} {colored_level:20} {record.name:20} {message}{location}"


class ProgressTracker:
    """Track progress of long-running operations."""

    def __init__(
        self, total: int, description: str = "Processing", logger: Optional[logging.Logger] = None
    ):
        """
        Initialize progress tracker.

        Args:
            total: Total number of items to process
            description: Description of the operation
            logger: Logger to use for progress updates
        """
        self.total = total
        self.description = description
        self.logger = logger or logging.getLogger(__name__)
        self.current = 0
        self.start_time = time.time()
        self.last_update = 0
        self.update_interval = 1.0  # seconds

    def update(self, increment: int = 1, message: Optional[str] = None):
        """
        Update progress.

        Args:
            increment: Number of items processed
            message: Optional status message
        """
        self.current += increment
        current_time = time.time()

        # Only log if enough time has passed or we're done
        if current_time - self.last_update >= self.update_interval or self.current >= self.total:

            percentage = (self.current / self.total) * 100
            elapsed = current_time - self.start_time

            if self.current > 0:
                eta = (elapsed / self.current) * (self.total - self.current)
                eta_str = f", ETA: {eta:.1f}s" if eta > 1 else ""
            else:
                eta_str = ""

            status = message or ""
            if status:
                status = f" - {status}"

            self.logger.info(
                f"{self.description}: {self.current}/{self.total} "
                f"({percentage:.1f}%) in {elapsed:.1f}s{eta_str}{status}",
                extra={
                    "progress_current": self.current,
                    "progress_total": self.total,
                    "progress_percentage": percentage,
                    "progress_elapsed": elapsed,
                },
            )

            self.last_update = current_time

    def finish(self, message: Optional[str] = None):
        """Mark progress as finished."""
        elapsed = time.time() - self.start_time
        rate = self.current / elapsed if elapsed > 0 else 0

        status = message or "completed"
        self.logger.info(
            f"{self.description} {status}: {self.current} items in {elapsed:.1f}s "
            f"({rate:.1f} items/sec)",
            extra={
                "progress_finished": True,
                "progress_total_time": elapsed,
                "progress_rate": rate,
            },
        )


def setup_logging(
    level: str = "INFO",
    log_file: Optional[Path] = None,
    verbose: bool = False,
    structured: bool = False,
) -> None:
    """
    Setup logging configuration.

    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Optional file to write logs to
        verbose: Enable verbose logging
        structured: Use structured JSON logging
    """
    # Convert level string to logging constant
    numeric_level = getattr(logging, level.upper(), logging.INFO)

    # Use DEBUG level if verbose
    if verbose:
        numeric_level = logging.DEBUG

    # Clear any existing handlers
    root_logger = logging.getLogger()
    root_logger.handlers.clear()

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(numeric_level)

    if structured:
        console_formatter = StructuredFormatter()
    else:
        console_formatter = ColoredFormatter()

    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)

    # File handler if specified
    if log_file:
        log_file = Path(log_file)
        log_file.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.handlers.RotatingFileHandler(
            log_file, maxBytes=10 * 1024 * 1024, backupCount=5
        )
        file_handler.setLevel(logging.DEBUG)  # Always debug level for files

        # Always use structured format for files
        file_formatter = StructuredFormatter()
        file_handler.setFormatter(file_formatter)
        root_logger.addHandler(file_handler)

    # Set root logger level
    root_logger.setLevel(numeric_level)

    # Configure third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("requests").setLevel(logging.WARNING)
    logging.getLogger("chromadb").setLevel(logging.WARNING)

    # Log setup completion
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured: level={level}, file={log_file}, structured={structured}")


def get_logger(name: str) -> logging.Logger:
    """
    Get a configured logger instance.

    Args:
        name: Logger name (usually __name__)

    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


@contextmanager
def log_performance(
    operation: str, logger: Optional[logging.Logger] = None, level: int = logging.INFO
):
    """
    Context manager to log operation performance.

    Args:
        operation: Description of the operation
        logger: Logger to use
        level: Log level for performance info
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    start_time = time.time()
    logger.log(level, f"Starting {operation}")

    try:
        yield
        elapsed = time.time() - start_time
        logger.log(
            level,
            f"Completed {operation} in {elapsed:.2f}s",
            extra={"operation": operation, "duration": elapsed},
        )
    except Exception as e:
        elapsed = time.time() - start_time
        logger.error(
            f"Failed {operation} after {elapsed:.2f}s: {e}",
            extra={"operation": operation, "duration": elapsed, "error": str(e)},
        )
        raise


def log_memory_usage(logger: Optional[logging.Logger] = None):
    """
    Log current memory usage.

    Args:
        logger: Logger to use
    """
    if logger is None:
        logger = logging.getLogger(__name__)

    try:
        import psutil

        process = psutil.Process()
        memory_info = process.memory_info()

        logger.debug(
            f"Memory usage: RSS={memory_info.rss / 1024 / 1024:.1f}MB, "
            f"VMS={memory_info.vms / 1024 / 1024:.1f}MB",
            extra={
                "memory_rss_mb": memory_info.rss / 1024 / 1024,
                "memory_vms_mb": memory_info.vms / 1024 / 1024,
            },
        )
    except ImportError:
        logger.debug("psutil not available, cannot log memory usage")
    except Exception as e:
        logger.debug(f"Error getting memory usage: {e}")


def create_progress_tracker(total: int, description: str = "Processing") -> ProgressTracker:
    """
    Create a progress tracker with default logger.

    Args:
        total: Total number of items
        description: Operation description

    Returns:
        ProgressTracker instance
    """
    logger = logging.getLogger("progress")
    return ProgressTracker(total, description, logger)
