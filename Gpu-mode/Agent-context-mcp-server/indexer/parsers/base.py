"""
Abstract Parser Interface

This module defines the base interface for all language-specific parsers
and provides a registry system for managing parser implementations.

Key classes:
- BaseParser: Abstract base class for all parsers
- ParserRegistry: Registry for managing parser implementations
- ParseResult: Result of parsing operations
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Type
from dataclasses import dataclass
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


@dataclass
class ParseResult:
    """Result of a parsing operation."""

    chunks: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    errors: List[str] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []


class BaseParser(ABC):
    """Abstract base class for language-specific parsers."""

    def __init__(self, max_chunk_size: int = 1000, overlap_size: int = 100):
        """
        Initialize parser.

        Args:
            max_chunk_size: Maximum size of chunks in tokens
            overlap_size: Overlap between adjacent chunks in tokens
        """
        self.max_chunk_size = max_chunk_size
        self.overlap_size = overlap_size
        self.language = self.get_language()

    @abstractmethod
    def get_language(self) -> str:
        """Get the language this parser handles."""
        pass

    @abstractmethod
    def parse(self, content: str, file_path: Path) -> ParseResult:
        """
        Parse content and extract semantic chunks.

        Args:
            content: File content to parse
            file_path: Path to the file being parsed

        Returns:
            ParseResult with extracted chunks and metadata
        """
        pass

    def can_parse(self, file_path: Path) -> bool:
        """
        Check if this parser can handle the given file.

        Args:
            file_path: Path to check

        Returns:
            True if parser can handle this file
        """
        return file_path.suffix.lower() in self.get_supported_extensions()

    @abstractmethod
    def get_supported_extensions(self) -> List[str]:
        """Get list of file extensions this parser supports."""
        pass

    def extract_imports(self, content: str) -> List[str]:
        """
        Extract import statements from content.

        Args:
            content: Source code content

        Returns:
            List of import statements
        """
        # Default implementation - override in subclasses
        return []

    def extract_docstring(self, content: str) -> Optional[str]:
        """
        Extract docstring from content.

        Args:
            content: Source code content

        Returns:
            Docstring if found, None otherwise
        """
        # Default implementation - override in subclasses
        return None

    def calculate_complexity(self, content: str) -> float:
        """
        Calculate complexity score for content.

        Args:
            content: Source code content

        Returns:
            Complexity score (0.0 to 1.0)
        """
        # Simple line-based complexity for default implementation
        lines = content.split("\n")
        non_empty_lines = [line for line in lines if line.strip()]

        # Basic complexity based on line count and nesting
        complexity = min(len(non_empty_lines) / 100.0, 1.0)

        # Add nesting complexity
        max_indent = 0
        for line in non_empty_lines:
            if line.strip():
                indent = len(line) - len(line.lstrip())
                max_indent = max(max_indent, indent)

        nesting_complexity = min(max_indent / 40.0, 0.5)

        return min(complexity + nesting_complexity, 1.0)

    def split_large_chunk(self, content: str, metadata: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Split large chunks into smaller pieces.

        Args:
            content: Content to split
            metadata: Metadata for the chunk

        Returns:
            List of smaller chunks
        """
        lines = content.split("\n")

        if len(lines) <= 50:  # Small enough, don't split
            return [{"content": content, "metadata": metadata}]

        chunks = []
        chunk_size = 30  # lines per chunk
        overlap = 5  # lines overlap

        for i in range(0, len(lines), chunk_size - overlap):
            chunk_lines = lines[i : i + chunk_size]
            chunk_content = "\n".join(chunk_lines)

            chunk_metadata = metadata.copy()
            chunk_metadata.update(
                {
                    "start_line": metadata.get("start_line", 1) + i,
                    "end_line": metadata.get("start_line", 1) + i + len(chunk_lines) - 1,
                    "is_split_chunk": True,
                    "original_chunk_type": metadata.get("chunk_type", "unknown"),
                }
            )

            chunks.append({"content": chunk_content, "metadata": chunk_metadata})

        return chunks


class ParserRegistry:
    """Registry for managing parser implementations."""

    def __init__(self):
        """Initialize empty registry."""
        self._parsers: Dict[str, Type[BaseParser]] = {}
        self._instances: Dict[str, BaseParser] = {}

    def register(self, language: str, parser_class: Type[BaseParser]) -> None:
        """
        Register a parser for a language.

        Args:
            language: Language identifier
            parser_class: Parser class to register
        """
        self._parsers[language.lower()] = parser_class
        logger.debug(f"Registered parser for language: {language}")

    def get_parser(self, language: str, **kwargs) -> Optional[BaseParser]:
        """
        Get parser instance for a language.

        Args:
            language: Language identifier
            **kwargs: Arguments to pass to parser constructor

        Returns:
            Parser instance or None if not found
        """
        language = language.lower()

        if language not in self._parsers:
            return None

        # Create instance if not cached
        cache_key = f"{language}_{hash(frozenset(kwargs.items()))}"
        if cache_key not in self._instances:
            parser_class = self._parsers[language]
            self._instances[cache_key] = parser_class(**kwargs)

        return self._instances[cache_key]

    def get_parser_for_file(self, file_path: Path, **kwargs) -> Optional[BaseParser]:
        """
        Get appropriate parser for a file.

        Args:
            file_path: Path to file
            **kwargs: Arguments to pass to parser constructor

        Returns:
            Parser instance or None if no suitable parser found
        """
        # Try each registered parser
        for language, parser_class in self._parsers.items():
            parser = self.get_parser(language, **kwargs)
            if parser and parser.can_parse(file_path):
                return parser

        # Fallback to generic parser if available
        return self.get_parser("generic", **kwargs)

    def list_languages(self) -> List[str]:
        """Get list of supported languages."""
        return list(self._parsers.keys())

    def clear_cache(self) -> None:
        """Clear parser instance cache."""
        self._instances.clear()
        logger.debug("Cleared parser instance cache")
