"""
Configuration Management and Settings

This module defines the configuration data models and provides functions
for loading and validating configuration from various sources.

Key classes:
- IndexConfig: Main configuration dataclass
- ConfigLoader: Configuration loading and merging
"""

import os
import yaml
from dataclasses import dataclass, field
from pathlib import Path
from typing import List, Optional, Dict, Any, Union, TYPE_CHECKING
import logging

if TYPE_CHECKING:
    import torch

logger = logging.getLogger(__name__)


@dataclass
class IndexConfig:
    """Configuration for the RAG indexer."""

    # Input settings
    repository_path: Path
    output_path: Path = field(default_factory=lambda: Path("./index_output"))

    # File filtering
    include_extensions: List[str] = field(
        default_factory=lambda: [
            ".py",
            ".js",
            ".ts",
            ".jsx",
            ".tsx",
            ".md",
            ".json",
            ".yaml",
            ".yml",
        ]
    )
    ignore_patterns: List[str] = field(
        default_factory=lambda: [
            "node_modules/",
            "*.pyc",
            ".git/",
            "__pycache__/",
            "venv/",
            ".env/",
            "dist/",
            "build/",
            ".pytest_cache/",
            ".mypy_cache/",
            ".coverage",
        ]
    )
    max_file_size_mb: float = 10.0

    # Chunking settings
    max_chunk_size: int = 1000
    overlap_size: int = 100
    preserve_structure: bool = True

    # Embedding settings
    embedding_model: str = "all-MiniLM-L6-v2"
    batch_size: int = 32
    device: str = "cpu"

    # Storage settings
    db_path: Path = field(default_factory=lambda: Path("./chroma_db"))
    collection_name: str = "codebase_chunks"

    # Performance settings
    max_workers: int = 4
    memory_limit_gb: float = 2.0

    def __post_init__(self) -> None:
        """Validate and normalize configuration after initialization."""
        # Convert string paths to Path objects
        if isinstance(self.repository_path, str):
            self.repository_path = Path(self.repository_path)
        if isinstance(self.output_path, str):
            self.output_path = Path(self.output_path)
        if isinstance(self.db_path, str):
            self.db_path = Path(self.db_path)

        # Validate repository path exists
        if not self.repository_path.exists():
            raise ValueError(f"Repository path does not exist: {self.repository_path}")

        # Ensure output directories exist
        self.output_path.mkdir(parents=True, exist_ok=True)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Validate numeric values
        if self.max_chunk_size <= 0:
            raise ValueError("max_chunk_size must be positive")
        if self.batch_size <= 0:
            raise ValueError("batch_size must be positive")
        if self.max_file_size_mb <= 0:
            raise ValueError("max_file_size_mb must be positive")

        # Normalize device setting
        if self.device.lower() in ["gpu", "cuda"]:
            self.device = "cuda"
        else:
            self.device = "cpu"

    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "repository_path": str(self.repository_path),
            "output_path": str(self.output_path),
            "include_extensions": self.include_extensions,
            "ignore_patterns": self.ignore_patterns,
            "max_file_size_mb": self.max_file_size_mb,
            "max_chunk_size": self.max_chunk_size,
            "overlap_size": self.overlap_size,
            "preserve_structure": self.preserve_structure,
            "embedding_model": self.embedding_model,
            "batch_size": self.batch_size,
            "device": self.device,
            "db_path": str(self.db_path),
            "collection_name": self.collection_name,
            "max_workers": self.max_workers,
            "memory_limit_gb": self.memory_limit_gb,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "IndexConfig":
        """Create configuration from dictionary."""
        # Convert path strings to Path objects
        if "repository_path" in data:
            data["repository_path"] = Path(data["repository_path"])
        if "output_path" in data:
            data["output_path"] = Path(data["output_path"])
        if "db_path" in data:
            data["db_path"] = Path(data["db_path"])

        return cls(**data)


def load_config(
    config_path: Optional[Union[str, Path]] = None,
    repository_path: Optional[Path] = None,
    output_path: Optional[Path] = None,
    **overrides,
) -> IndexConfig:
    """
    Load configuration from file and environment variables.

    Args:
        config_path: Path to configuration file (YAML)
        repository_path: Override repository path
        output_path: Override output path
        **overrides: Additional configuration overrides

    Returns:
        Loaded and validated configuration
    """
    # Start with default configuration
    config_data = get_default_config()

    # Load from file if provided
    if config_path:
        config_path = Path(config_path)
        if config_path.exists():
            try:
                with open(config_path, "r") as f:
                    file_config = yaml.safe_load(f)
                if file_config:
                    config_data.update(file_config)
                logger.info(f"Loaded configuration from {config_path}")
            except Exception as e:
                logger.error(f"Error loading config file {config_path}: {e}")
                raise
        else:
            logger.warning(f"Config file not found: {config_path}")

    # Apply environment variable overrides
    env_overrides = load_from_environment()
    config_data.update(env_overrides)

    # Apply CLI overrides
    if repository_path:
        config_data["repository_path"] = repository_path
    if output_path:
        config_data["output_path"] = output_path

    # Apply additional overrides
    config_data.update(overrides)

    # Create and validate configuration
    try:
        config = IndexConfig.from_dict(config_data)
        logger.info("Configuration loaded successfully")
        return config
    except Exception as e:
        logger.error(f"Configuration validation failed: {e}")
        raise


def load_from_environment() -> Dict[str, Any]:
    """Load configuration overrides from environment variables."""
    env_config = {}

    # Define environment variable mappings
    env_mappings = {
        "INDEXER_REPOSITORY_PATH": "repository_path",
        "INDEXER_OUTPUT_PATH": "output_path",
        "INDEXER_DB_PATH": "db_path",
        "INDEXER_EMBEDDING_MODEL": "embedding_model",
        "INDEXER_DEVICE": "device",
        "INDEXER_BATCH_SIZE": ("batch_size", int),
        "INDEXER_MAX_CHUNK_SIZE": ("max_chunk_size", int),
        "INDEXER_MAX_FILE_SIZE_MB": ("max_file_size_mb", float),
        "INDEXER_MAX_WORKERS": ("max_workers", int),
        "INDEXER_MEMORY_LIMIT_GB": ("memory_limit_gb", float),
        "INDEXER_COLLECTION_NAME": "collection_name",
    }

    for env_var, config_key in env_mappings.items():
        value = os.getenv(env_var)
        if value:
            if isinstance(config_key, tuple):
                # Type conversion needed
                key, type_func = config_key
                try:
                    env_config[key] = type_func(value)
                except ValueError:
                    logger.warning(f"Invalid value for {env_var}: {value}")
            else:
                env_config[config_key] = value

    # Handle list environment variables
    include_ext = os.getenv("INDEXER_INCLUDE_EXTENSIONS")
    if include_ext:
        env_config["include_extensions"] = [ext.strip() for ext in include_ext.split(",")]

    ignore_patterns = os.getenv("INDEXER_IGNORE_PATTERNS")
    if ignore_patterns:
        env_config["ignore_patterns"] = [pattern.strip() for pattern in ignore_patterns.split(",")]

    if env_config:
        logger.debug(f"Loaded {len(env_config)} configuration values from environment")

    return env_config


def get_default_config() -> Dict[str, Any]:
    """Get default configuration values."""
    return {
        "output_path": "./index_output",
        "include_extensions": [
            ".py",
            ".js",
            ".ts",
            ".jsx",
            ".tsx",
            ".md",
            ".json",
            ".yaml",
            ".yml",
            ".toml",
            ".txt",
            ".rst",
            ".go",
            ".rs",
            ".java",
            ".cpp",
            ".c",
            ".h",
        ],
        "ignore_patterns": [
            "node_modules/",
            "*.pyc",
            ".git/",
            "__pycache__/",
            "venv/",
            ".env/",
            "dist/",
            "build/",
            ".pytest_cache/",
            ".mypy_cache/",
            ".coverage",
            "*.log",
            "*.tmp",
            ".DS_Store",
            "Thumbs.db",
        ],
        "max_file_size_mb": 10.0,
        "max_chunk_size": 1000,
        "overlap_size": 100,
        "preserve_structure": True,
        "embedding_model": "all-MiniLM-L6-v2",
        "batch_size": 32,
        "device": "cpu",
        "db_path": "./chroma_db",
        "collection_name": "codebase_chunks",
        "max_workers": 4,
        "memory_limit_gb": 2.0,
    }


def create_default_config(output_path: Path) -> None:
    """
    Create a default configuration file.

    Args:
        output_path: Path where to save the configuration file
    """
    config_template = {
        "# RAG Indexer Configuration": None,
        "# Input settings": None,
        "repository_path": "/path/to/repo",
        "output_path": "./index_output",
        "# File filtering": None,
        "include_extensions": [
            ".py",
            ".js",
            ".ts",
            ".jsx",
            ".tsx",
            ".md",
            ".json",
            ".yaml",
            ".yml",
        ],
        "ignore_patterns": ["node_modules/", "*.pyc", ".git/", "__pycache__/", "venv/", ".env/"],
        "max_file_size_mb": 10.0,
        "# Chunking settings": None,
        "max_chunk_size": 1000,
        "overlap_size": 100,
        "preserve_structure": True,
        "# Embedding settings": None,
        "embedding_model": "all-MiniLM-L6-v2",
        "batch_size": 32,
        "device": "cpu",  # or 'cuda'
        "# Storage settings": None,
        "db_path": "./chroma_db",
        "collection_name": "codebase_chunks",
        "# Performance settings": None,
        "max_workers": 4,
        "memory_limit_gb": 2.0,
    }

    # Filter out comment entries and empty strings for YAML output
    yaml_config = {k: v for k, v in config_template.items() if v is not None and k != ""}

    try:
        with open(output_path, "w") as f:
            f.write("# RAG Indexer Configuration\n")
            f.write("# Customize these settings for your use case\n\n")
            yaml.dump(yaml_config, f, default_flow_style=False, sort_keys=False)

        logger.info(f"Created default configuration file: {output_path}")

    except Exception as e:
        logger.error(f"Error creating config file {output_path}: {e}")
        raise


def validate_config(config: IndexConfig) -> List[str]:
    """
    Validate configuration and return list of warnings/errors.

    Args:
        config: Configuration to validate

    Returns:
        List of validation messages
    """
    warnings = []

    # Check if repository path is accessible
    if not config.repository_path.is_dir():
        warnings.append(f"Repository path is not a directory: {config.repository_path}")

    # Check embedding model
    if config.embedding_model.startswith("text-embedding-"):
        # OpenAI model - check for API key
        if not os.getenv("OPENAI_API_KEY"):
            warnings.append("OpenAI embedding model specified but OPENAI_API_KEY not set")

    # Check device setting
    if config.device == "cuda":
        try:
            import torch

            if not torch.cuda.is_available():
                warnings.append("CUDA device specified but not available, falling back to CPU")
        except ImportError:
            warnings.append("CUDA device specified but PyTorch not available")

    # Check memory limits
    if config.memory_limit_gb < 1.0:
        warnings.append("Memory limit is very low, may cause performance issues")

    # Check batch size
    if config.batch_size > 128:
        warnings.append("Large batch size may cause memory issues")

    return warnings
